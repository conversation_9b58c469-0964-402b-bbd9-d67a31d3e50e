<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 保存图片到相册的权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/ic_logo"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_logo"
        android:supportsRtl="true"
        android:theme="@style/Theme.ColoringProject"
        android:networkSecurityConfig="@xml/network_security_config"
        android:largeHeap="true"
        android:hardwareAccelerated="true"
        tools:targetApi="31">
        <!-- Splash启动页面 -->
        <activity
            android:name=".OptimizedSplashActivity"
            android:exported="true"
            android:theme="@style/Theme.ColoringProject.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 主页面 -->
        <activity
            android:name=".EnhancedMainActivity"
            android:exported="false" />
        <activity
            android:name=".MainActivity"
            android:exported="false" />
        <activity
            android:name=".FileTestActivity"
            android:exported="true" />
        <activity
            android:name=".JsonValidatorActivity"
            android:exported="false" />
        <activity
            android:name=".ApiTestActivity"
            android:exported="true" >
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
        </activity>
        <activity
            android:name=".CategoryTestActivity"
            android:exported="true" />
        <activity
            android:name=".AssetsDebugActivity"
            android:exported="true" />
<!--        <activity-->
<!--            android:name=".EnhancedMainActivity"-->
<!--            android:exported="false" />-->

        <activity
            android:name=".SimpleMainActivity"
            android:exported="true"
            android:theme="@style/Theme.ColoringProject">

<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
        </activity>
        
        <!-- 测试相关Activity -->
        <activity
            android:name=".TestLauncherActivity"
            android:exported="true"
            android:label="测试中心"
            android:theme="@style/Theme.ColoringProject">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name=".TestRunnerActivity"
            android:exported="true"
            android:label="综合测试套件"
            android:theme="@style/Theme.ColoringProject" />
            
        <activity
            android:name=".BenchmarkActivity"
            android:exported="true"
            android:label="性能基准测试"
            android:theme="@style/Theme.ColoringProject" />
            
        <activity
            android:name=".MemoryAnalysisActivity"
            android:exported="true"
            android:label="内存分析"
            android:theme="@style/Theme.ColoringProject" />

        <activity
            android:name=".NetworkTestActivity"
            android:exported="true"
            android:label="网络功能测试"
            android:theme="@style/Theme.ColoringProject" />

        <activity
            android:name=".ProjectTypeTestActivity"
            android:exported="true"
            android:label="ProjectType测试"
            android:theme="@style/Theme.ColoringProject" />

        <activity
            android:name=".ServerTestActivity"
            android:exported="true"
            android:label="服务器测试 (8083)"
            android:theme="@style/Theme.ColoringProject" />

        <activity
            android:name=".HybridDataTestActivity"
            android:exported="true"
            android:label="混合数据测试"
            android:theme="@style/Theme.ColoringProject" />
    </application>

</manifest>