package com.example.coloringproject

import android.content.ComponentCallbacks2
import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.coloringproject.config.NetworkConfig
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.utils.OptimizedResourceManager
import com.example.coloringproject.utils.AsyncProgressSaver
import com.example.coloringproject.view.ColoringView
import kotlinx.coroutines.*

/**
 * 优化的主Activity
 * 实现快速启动、异步初始化、统一资源管理
 */
class OptimizedMainActivity : AppCompatActivity(), ComponentCallbacks2 {

    companion object {
        private const val TAG = "OptimizedMainActivity"
        private const val DEFAULT_PROJECT_ID = "animal1"
    }

    // 延迟初始化，避免启动时阻塞
    private val resourceManager by lazy {
        OptimizedResourceManager.getInstance(this)
    }

    private val progressSaver by lazy {
        AsyncProgressSaver(this)
    }

    // UI组件
    private lateinit var coloringView: ColoringView
    private lateinit var loadingProgressBar: ProgressBar
    private lateinit var loadingText: TextView
    private lateinit var splashContainer: View

    // 状态管理
    private var currentProjectId: String = DEFAULT_PROJECT_ID
    private var isInitialized = false
    private var initializationJob: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "🚀 OptimizedMainActivity启动")

        // 1. 快速UI初始化
        initViews()

        // 2. 显示启动画面
        showSplashContent()

        // 3. 异步初始化核心功能
        initializationJob = lifecycleScope.launch {
            initializeAsync()
        }
    }

    /**
     * 快速UI初始化
     */
    private fun initViews() {
        setContentView(R.layout.activity_optimized_main)

        // 初始化UI组件
        coloringView = findViewById(R.id.coloringView)
        loadingProgressBar = findViewById(R.id.loadingProgressBar)
        loadingText = findViewById(R.id.loadingText)
        splashContainer = findViewById(R.id.splashContainer)

        // 设置初始状态
        coloringView.visibility = View.GONE
        splashContainer.visibility = View.VISIBLE

        Log.d(TAG, "✅ UI初始化完成")
    }

    /**
     * 显示启动内容
     */
    private fun showSplashContent() {
        updateProgress(0, "正在启动...")

        // 显示网络功能状态
        val networkStatus = if (NetworkConfig.isNetworkFeatureEnabled()) {
            "网络功能已启用"
        } else {
            "仅本地模式"
        }

        Log.i(TAG, "=== 应用启动状态 ===")
        Log.i(TAG, "网络功能: ${if (NetworkConfig.isNetworkFeatureEnabled()) "启用" else "禁用"}")
        Log.i(TAG, "资源下载: ${if (NetworkConfig.isDownloadEnabled()) "启用" else "禁用"}")
        Log.i(TAG, "远程API: ${if (NetworkConfig.isApiEnabled()) "启用" else "禁用"}")
        Log.i(TAG, "加载策略: 统一资源管理器")
        Log.i(TAG, "==================")
    }

    /**
     * 异步初始化
     */
    private suspend fun initializeAsync() {
        try {
            Log.d(TAG, "🔧 开始异步初始化...")

            // 显示进度
            updateProgress(20, "正在初始化资源管理器...")

            // 延迟一点时间，让UI先显示
            delay(100)

            // 初始化资源管理器 (延迟初始化，不会立即扫描)
            updateProgress(40, "正在准备资源...")

            // 检查是否有保存的项目ID
            val savedProjectId = getSavedProjectId()
            currentProjectId = savedProjectId ?: DEFAULT_PROJECT_ID

            updateProgress(60, "正在加载项目...")

            // 加载默认项目
            loadProjectAsync(currentProjectId)

            updateProgress(100, "完成!")

            // 短暂延迟后隐藏启动画面
            delay(500)
            hideSplashContent()

            isInitialized = true
            Log.d(TAG, "✅ 异步初始化完成")

        } catch (e: Exception) {
            Log.e(TAG, "❌ 初始化失败", e)
            handleInitializationError(e)
        }
    }

    /**
     * 异步加载项目
     */
    private suspend fun loadProjectAsync(projectId: String) {
        try {
            Log.d(TAG, "🔍 开始加载项目: $projectId")

            when (val result = resourceManager.loadProject(projectId)) {
                is OptimizedResourceManager.ResourceLoadResult.Success -> {
                    Log.d(TAG, "✅ 项目加载成功: $projectId")

                    withContext(Dispatchers.Main) {
                        setupProject(
                            result.projectData.coloringData,
                            result.projectData.outlineBitmap
                        )
                        loadSavedProgress(projectId)
                    }
                }

                is OptimizedResourceManager.ResourceLoadResult.RequiresDownload -> {
                    Log.w(TAG, "🌐 项目需要下载: $projectId")

                    withContext(Dispatchers.Main) {
                        showDownloadDialog(projectId, result.reason)
                    }
                }

                is OptimizedResourceManager.ResourceLoadResult.Error -> {
                    Log.e(TAG, "❌ 项目加载失败: ${result.message}")

                    // 尝试加载默认项目
                    if (projectId != DEFAULT_PROJECT_ID) {
                        Log.d(TAG, "🔄 尝试加载默认项目: $DEFAULT_PROJECT_ID")
                        loadProjectAsync(DEFAULT_PROJECT_ID)
                    } else {
                        withContext(Dispatchers.Main) {
                            showProjectLoadError("加载失败", result.message)
                        }
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "加载项目异常: $projectId", e)

            withContext(Dispatchers.Main) {
                showProjectLoadError("加载错误", "加载项目时发生错误: ${e.message}")
            }
        }
    }

    /**
     * 设置项目
     */
    private fun setupProject(coloringData: ColoringData, outlineBitmap: Bitmap?) {
        try {
            if (::coloringView.isInitialized && outlineBitmap != null) {
                // 使用ColoringView的正确API
                coloringView.setColoringData(coloringData, outlineBitmap)
                Log.d(TAG, "✅ 项目设置完成: ${coloringData.regions?.size ?: 0} 个区域")
            } else {
                Log.w(TAG, "ColoringView未初始化或轮廓图片为空")
            }

        } catch (e: Exception) {
            Log.e(TAG, "设置项目失败", e)
            Toast.makeText(this, "设置项目失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 加载保存的进度
     */
    private fun loadSavedProgress(projectId: String) {
        lifecycleScope.launch {
            try {
                // 使用异步进度加载器
                progressSaver.loadProgressAsync(projectId) { progressData ->
                    if (progressData != null && ::coloringView.isInitialized) {
                        // 使用ColoringView的安全恢复方法
                        val success = coloringView.restoreProgressSafely(progressData.filledRegions)
                        if (success) {
                            Log.d(TAG, "✅ 进度恢复成功: ${progressData.filledRegions.size} 个区域")
                        } else {
                            Log.w(TAG, "进度恢复失败，数据可能不匹配")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载进度失败", e)
            }
        }
    }

    /**
     * 更新进度显示
     */
    private fun updateProgress(progress: Int, message: String) {
        loadingProgressBar.progress = progress
        loadingText.text = message
        Log.d(TAG, "📊 进度: $progress% - $message")
    }

    /**
     * 隐藏启动画面
     */
    private fun hideSplashContent() {
        splashContainer.visibility = View.GONE
        coloringView.visibility = View.VISIBLE
        Log.d(TAG, "🎨 切换到涂色界面")
    }

    /**
     * 显示下载对话框
     */
    private fun showDownloadDialog(projectId: String, reason: String) {
        AlertDialog.Builder(this)
            .setTitle("需要下载项目")
            .setMessage("项目 $projectId 需要从服务器下载。\n\n原因: $reason")
            .setPositiveButton("下载") { _, _ ->
                downloadProject(projectId)
            }
            .setNegativeButton("取消") { _, _ ->
                // 尝试加载默认项目
                if (projectId != DEFAULT_PROJECT_ID) {
                    lifecycleScope.launch {
                        loadProjectAsync(DEFAULT_PROJECT_ID)
                    }
                }
            }
            .show()
    }

    /**
     * 下载项目
     */
    private fun downloadProject(projectId: String) {
        lifecycleScope.launch {
            try {
                updateProgress(0, "正在下载项目...")
                splashContainer.visibility = View.VISIBLE

                val result = resourceManager.downloadProject(projectId) { progress ->
                    lifecycleScope.launch {
                        updateProgress((progress * 100).toInt(), "下载中...")
                    }
                }

                if (result.isSuccess) {
                    val projectData = result.getOrNull()!!
                    setupProject(projectData.coloringData, projectData.outlineBitmap)
                    hideSplashContent()
                    Toast.makeText(this@OptimizedMainActivity, "下载完成", Toast.LENGTH_SHORT)
                        .show()
                } else {
                    hideSplashContent()
                    Toast.makeText(
                        this@OptimizedMainActivity,
                        "下载失败: ${result.exceptionOrNull()?.message}",
                        Toast.LENGTH_LONG
                    ).show()
                }

            } catch (e: Exception) {
                Log.e(TAG, "下载项目失败", e)
                hideSplashContent()
                Toast.makeText(
                    this@OptimizedMainActivity,
                    "下载出错: ${e.message}",
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }

    /**
     * 显示项目加载错误
     */
    private fun showProjectLoadError(title: String, message: String) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("重试") { _, _ ->
                lifecycleScope.launch {
                    loadProjectAsync(currentProjectId)
                }
            }
            .setNegativeButton("使用默认项目") { _, _ ->
                if (currentProjectId != DEFAULT_PROJECT_ID) {
                    lifecycleScope.launch {
                        loadProjectAsync(DEFAULT_PROJECT_ID)
                    }
                }
            }
            .show()
    }

    /**
     * 处理初始化错误
     */
    private fun handleInitializationError(error: Exception) {
        runOnUiThread {
            AlertDialog.Builder(this)
                .setTitle("初始化失败")
                .setMessage("应用初始化失败: ${error.message}")
                .setPositiveButton("重试") { _, _ ->
                    recreate()
                }
                .setNegativeButton("退出") { _, _ ->
                    finish()
                }
                .show()
        }
    }

    /**
     * 获取保存的项目ID
     */
    private fun getSavedProjectId(): String? {
        return try {
            val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
            prefs.getString("last_project_id", null)
        } catch (e: Exception) {
            Log.e(TAG, "获取保存的项目ID失败", e)
            null
        }
    }

    /**
     * 保存当前项目ID
     */
    private fun saveCurrentProjectId(projectId: String) {
        try {
            val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
            prefs.edit().putString("last_project_id", projectId).apply()
        } catch (e: Exception) {
            Log.e(TAG, "保存项目ID失败", e)
        }
    }

    override fun onPause() {
        super.onPause()

        // 异步保存进度
        if (isInitialized && ::coloringView.isInitialized) {
            try {
                // 获取当前填充的区域
                val filledRegions = coloringView.getFilledRegions()

                // 创建简化的涂色数据用于保存
                val mockColoringData = ColoringData(
                    regions = emptyList(),
                    colorPalette = emptyList(),
                    metadata = null
                )

                progressSaver.saveProgressAsync(
                    projectName = currentProjectId,
                    coloringData = mockColoringData,
                    filledRegions = filledRegions
                )

                // 保存当前项目ID
                saveCurrentProjectId(currentProjectId)

                Log.d(TAG, "保存进度: ${filledRegions.size} 个已填充区域")
            } catch (e: Exception) {
                Log.e(TAG, "保存进度失败", e)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 取消初始化任务
        initializationJob?.cancel()

        Log.d(TAG, "🔚 OptimizedMainActivity销毁")
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)

        // 内存压力处理
        resourceManager.onMemoryPressure(level)

        Log.d(TAG, "🧹 内存压力处理: level=$level")
    }
}
