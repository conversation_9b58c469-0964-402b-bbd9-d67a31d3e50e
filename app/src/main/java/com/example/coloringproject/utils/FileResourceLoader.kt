package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.ColoringDataDeserializer
import com.google.gson.GsonBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream

/**
 * 文件资源加载器
 * 专门处理从文件系统加载资源的逻辑
 */
class FileResourceLoader(private val context: Context) {
    
    companion object {
        private const val TAG = "FileResourceLoader"
    }
    
    private val gson = GsonBuilder()
        .registerTypeAdapter(ColoringData::class.java, ColoringDataDeserializer())
        .create()
    
    /**
     * 从文件加载涂色数据
     */
    suspend fun loadColoringDataFromFile(file: File): ColoringData? = withContext(Dispatchers.IO) {
        try {
            if (!file.exists() || !file.canRead()) {
                Log.w(TAG, "文件不存在或不可读: ${file.absolutePath}")
                return@withContext null
            }
            
            val jsonString = file.bufferedReader().use { it.readText() }
            
            if (jsonString.isBlank()) {
                Log.w(TAG, "JSON文件为空: ${file.absolutePath}")
                return@withContext null
            }
            
            val coloringData = gson.fromJson(jsonString, ColoringData::class.java)
            
            if (coloringData != null) {
                Log.d(TAG, "✅ 成功从文件加载JSON: ${file.name}")
                return@withContext coloringData
            } else {
                Log.w(TAG, "JSON解析结果为null: ${file.absolutePath}")
                return@withContext null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "从文件加载涂色数据失败: ${file.absolutePath}", e)
            null
        }
    }
    
    /**
     * 从文件加载位图
     */
    suspend fun loadBitmapFromFile(file: File): Bitmap? = withContext(Dispatchers.IO) {
        try {
            if (!file.exists() || !file.canRead()) {
                Log.w(TAG, "图片文件不存在或不可读: ${file.absolutePath}")
                return@withContext null
            }
            
            if (file.length() == 0L) {
                Log.w(TAG, "图片文件为空: ${file.absolutePath}")
                return@withContext null
            }
            
            // 使用BitmapFactory.Options进行优化加载
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            
            // 先获取图片尺寸
            BitmapFactory.decodeFile(file.absolutePath, options)
            
            // 计算合适的采样率
            options.inSampleSize = calculateInSampleSize(options, 1024, 1024)
            options.inJustDecodeBounds = false
            options.inPreferredConfig = Bitmap.Config.RGB_565 // 节省内存
            
            val bitmap = BitmapFactory.decodeFile(file.absolutePath, options)
            
            if (bitmap != null) {
                Log.d(TAG, "✅ 成功从文件加载图片: ${file.name} (${bitmap.width}x${bitmap.height})")
                return@withContext bitmap
            } else {
                Log.w(TAG, "图片解码失败: ${file.absolutePath}")
                return@withContext null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "从文件加载位图失败: ${file.absolutePath}", e)
            null
        }
    }
    
    /**
     * 验证文件完整性
     */
    suspend fun validateFiles(jsonFile: File, pngFile: File): Boolean = withContext(Dispatchers.IO) {
        try {
            // 检查文件存在性
            if (!jsonFile.exists() || !pngFile.exists()) {
                Log.w(TAG, "文件不存在 - JSON: ${jsonFile.exists()}, PNG: ${pngFile.exists()}")
                return@withContext false
            }
            
            // 检查文件大小
            if (jsonFile.length() == 0L || pngFile.length() == 0L) {
                Log.w(TAG, "文件为空 - JSON: ${jsonFile.length()}, PNG: ${pngFile.length()}")
                return@withContext false
            }
            
            // 检查文件可读性
            if (!jsonFile.canRead() || !pngFile.canRead()) {
                Log.w(TAG, "文件不可读 - JSON: ${jsonFile.canRead()}, PNG: ${pngFile.canRead()}")
                return@withContext false
            }
            
            Log.d(TAG, "✅ 文件验证通过: ${jsonFile.name}, ${pngFile.name}")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "文件验证失败", e)
            false
        }
    }
    
    /**
     * 计算合适的采样率
     */
    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1
        
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            
            while ((halfHeight / inSampleSize) >= reqHeight && 
                   (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
}
