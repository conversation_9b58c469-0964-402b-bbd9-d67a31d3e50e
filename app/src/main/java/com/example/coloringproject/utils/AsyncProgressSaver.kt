package com.example.coloringproject.utils

import android.content.Context
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.google.gson.Gson
import kotlinx.coroutines.*
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.system.measureTimeMillis

/**
 * 异步进度保存器
 * 实现完全异步的进度保存，避免主线程阻塞
 */
class AsyncProgressSaver(private val context: Context) {
    
    companion object {
        private const val TAG = "AsyncProgressSaver"
        private const val PROGRESS_FILE_SUFFIX = "_progress.json"
        private const val FULL_PROGRESS_FILE_SUFFIX = "_full_progress.json"
        private const val MIN_SAVE_INTERVAL = 2000L // 最小保存间隔2秒
        private const val BATCH_SAVE_DELAY = 500L // 批量保存延迟500ms
    }
    
    // 异步保存作用域
    private val saveScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val gson = Gson()
    
    // 保存状态管理
    private val lastSaveTime = ConcurrentHashMap<String, AtomicLong>()
    private val pendingSaves = ConcurrentHashMap<String, Job>()
    
    // 性能统计
    private var totalSaves = 0
    private var totalSaveTime = 0L
    
    /**
     * 进度数据类
     */
    data class ProgressData(
        val projectName: String,
        val filledRegions: Set<Int>,
        val timestamp: Long,
        val version: String = "1.0"
    )
    
    /**
     * 完整进度数据类
     */
    data class FullProgressData(
        val projectName: String,
        val filledRegions: Set<Int>,
        val coloringData: ColoringData?,
        val timestamp: Long,
        val saveCount: Int,
        val version: String = "1.0"
    )
    
    /**
     * 异步保存进度 - 主要入口方法
     */
    fun saveProgressAsync(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        onComplete: (Boolean) -> Unit = {}
    ) {
        // 检查保存间隔
        val lastTime = lastSaveTime.getOrPut(projectName) { AtomicLong(0) }
        val now = System.currentTimeMillis()
        
        if (now - lastTime.get() < MIN_SAVE_INTERVAL) {
            Log.d(TAG, "⏰ 保存间隔太短，跳过: $projectName")
            onComplete(false)
            return
        }
        
        // 取消之前的保存任务
        pendingSaves[projectName]?.cancel()
        
        // 创建新的保存任务
        val saveJob = saveScope.launch {
            try {
                val success = performAsyncSave(projectName, coloringData, filledRegions)
                
                if (success) {
                    lastTime.set(now)
                }
                
                withContext(Dispatchers.Main) {
                    onComplete(success)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "异步保存失败: $projectName", e)
                withContext(Dispatchers.Main) {
                    onComplete(false)
                }
            } finally {
                pendingSaves.remove(projectName)
            }
        }
        
        pendingSaves[projectName] = saveJob
    }
    
    /**
     * 批量保存进度 (延迟执行，避免频繁保存)
     */
    fun saveProgressBatched(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        onComplete: (Boolean) -> Unit = {}
    ) {
        // 取消之前的批量保存任务
        pendingSaves[projectName]?.cancel()
        
        // 创建延迟保存任务
        val batchJob = saveScope.launch {
            delay(BATCH_SAVE_DELAY)
            
            try {
                val success = performAsyncSave(projectName, coloringData, filledRegions)
                
                withContext(Dispatchers.Main) {
                    onComplete(success)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "批量保存失败: $projectName", e)
                withContext(Dispatchers.Main) {
                    onComplete(false)
                }
            } finally {
                pendingSaves.remove(projectName)
            }
        }
        
        pendingSaves[projectName] = batchJob
    }
    
    /**
     * 执行异步保存
     */
    private suspend fun performAsyncSave(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>
    ): Boolean = withContext(Dispatchers.IO) {
        
        val saveTime = measureTimeMillis {
            try {
                Log.d(TAG, "💾 开始异步保存: $projectName (${filledRegions.size} 个区域)")
                
                // 1. 并行创建保存数据
                val lightweightDataDeferred = async {
                    createLightweightProgress(projectName, filledRegions)
                }
                val fullDataDeferred = async {
                    createFullProgress(projectName, filledRegions, coloringData)
                }
                
                // 2. 等待数据创建完成
                val lightweightData = lightweightDataDeferred.await()
                val fullData = fullDataDeferred.await()
                
                // 3. 并行序列化
                val lightJsonDeferred = async {
                    gson.toJson(lightweightData)
                }
                val fullJsonDeferred = async {
                    gson.toJson(fullData)
                }
                
                val lightJson = lightJsonDeferred.await()
                val fullJson = fullJsonDeferred.await()
                
                // 4. 并行写入文件
                val saveDir = getSaveDirectory()
                val lightFile = File(saveDir, "${projectName}${PROGRESS_FILE_SUFFIX}")
                val fullFile = File(saveDir, "${projectName}${FULL_PROGRESS_FILE_SUFFIX}")
                
                val writeJobs = listOf(
                    async { writeToFileOptimized(lightFile, lightJson) },
                    async { writeToFileOptimized(fullFile, fullJson) }
                )
                
                writeJobs.awaitAll()
                
                // 5. 更新统计
                totalSaves++
                totalSaveTime += saveTime
                
                Log.d(TAG, "✅ 异步保存完成: $projectName (耗时: ${saveTime}ms)")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "异步保存执行失败: $projectName", e)
                false
            }
        }
        
        true
    }
    
    /**
     * 异步加载进度
     */
    fun loadProgressAsync(
        projectName: String,
        onComplete: (ProgressData?) -> Unit
    ) {
        saveScope.launch {
            try {
                val progressData = performAsyncLoad(projectName)
                
                withContext(Dispatchers.Main) {
                    onComplete(progressData)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "异步加载失败: $projectName", e)
                withContext(Dispatchers.Main) {
                    onComplete(null)
                }
            }
        }
    }
    
    /**
     * 执行异步加载
     */
    private suspend fun performAsyncLoad(projectName: String): ProgressData? = withContext(Dispatchers.IO) {
        try {
            val saveDir = getSaveDirectory()
            val lightFile = File(saveDir, "${projectName}${PROGRESS_FILE_SUFFIX}")
            
            if (!lightFile.exists()) {
                Log.d(TAG, "进度文件不存在: $projectName")
                return@withContext null
            }
            
            val jsonString = lightFile.bufferedReader().use { it.readText() }
            val progressData = gson.fromJson(jsonString, ProgressData::class.java)
            
            if (progressData != null) {
                Log.d(TAG, "✅ 异步加载成功: $projectName (${progressData.filledRegions.size} 个区域)")
            }
            
            progressData
            
        } catch (e: Exception) {
            Log.e(TAG, "异步加载执行失败: $projectName", e)
            null
        }
    }
    
    /**
     * 创建轻量级进度数据
     */
    private fun createLightweightProgress(
        projectName: String,
        filledRegions: Set<Int>
    ): ProgressData {
        return ProgressData(
            projectName = projectName,
            filledRegions = filledRegions,
            timestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * 创建完整进度数据
     */
    private fun createFullProgress(
        projectName: String,
        filledRegions: Set<Int>,
        coloringData: ColoringData
    ): FullProgressData {
        return FullProgressData(
            projectName = projectName,
            filledRegions = filledRegions,
            coloringData = coloringData,
            timestamp = System.currentTimeMillis(),
            saveCount = totalSaves + 1
        )
    }
    
    /**
     * 优化的文件写入
     */
    private suspend fun writeToFileOptimized(file: File, content: String) = withContext(Dispatchers.IO) {
        try {
            // 确保目录存在
            file.parentFile?.mkdirs()
            
            // 使用缓冲写入提高性能
            file.bufferedWriter(bufferSize = 8192).use { writer ->
                writer.write(content)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "文件写入失败: ${file.absolutePath}", e)
            throw e
        }
    }
    
    /**
     * 获取保存目录
     */
    private fun getSaveDirectory(): File {
        val saveDir = File(context.filesDir, "progress_saves")
        if (!saveDir.exists()) {
            saveDir.mkdirs()
        }
        return saveDir
    }
    
    /**
     * 强制保存所有待保存的进度
     */
    suspend fun flushAllPendingSaves() {
        try {
            Log.d(TAG, "🔄 强制保存所有待保存进度...")
            
            val jobs = pendingSaves.values.toList()
            jobs.joinAll()
            
            Log.d(TAG, "✅ 所有待保存进度已完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "强制保存失败", e)
        }
    }
    
    /**
     * 清理过期的进度文件
     */
    suspend fun cleanupExpiredProgress(maxAge: Long = 30 * 24 * 60 * 60 * 1000L) = withContext(Dispatchers.IO) {
        try {
            val saveDir = getSaveDirectory()
            val now = System.currentTimeMillis()
            var cleanedCount = 0
            var freedSpace = 0L
            
            saveDir.listFiles()?.forEach { file ->
                if (file.isFile && file.lastModified() < now - maxAge) {
                    freedSpace += file.length()
                    if (file.delete()) {
                        cleanedCount++
                    }
                }
            }
            
            Log.d(TAG, "🧹 清理完成: 删除${cleanedCount}个文件，释放${freedSpace / 1024}KB空间")
            
        } catch (e: Exception) {
            Log.e(TAG, "清理过期进度失败", e)
        }
    }
    
    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): String {
        val avgSaveTime = if (totalSaves > 0) totalSaveTime / totalSaves else 0
        return "总保存次数: $totalSaves, 平均耗时: ${avgSaveTime}ms"
    }
    
    /**
     * 取消所有保存任务
     */
    fun cancelAllSaves() {
        pendingSaves.values.forEach { it.cancel() }
        pendingSaves.clear()
        Log.d(TAG, "🚫 所有保存任务已取消")
    }
    
    /**
     * 销毁保存器
     */
    fun destroy() {
        cancelAllSaves()
        saveScope.cancel()
        Log.d(TAG, "🔚 AsyncProgressSaver已销毁")
    }
}
