package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.util.LruCache
import androidx.collection.LruCache as AndroidXLruCache
import com.example.coloringproject.config.NetworkConfig
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.HybridProject
import com.example.coloringproject.data.ProjectInfo
import com.example.coloringproject.data.ResourceSource
import com.example.coloringproject.network.ResourceDownloadManager
import kotlinx.coroutines.*
import java.io.File
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 优化的统一资源管理器
 * 替代多个重叠的资源管理器，提供统一的资源访问入口
 */
class OptimizedResourceManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "OptimizedResourceManager"
        private const val CACHE_SIZE = 50
        
        @Volatile
        private var INSTANCE: OptimizedResourceManager? = null
        
        fun getInstance(context: Context): OptimizedResourceManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: OptimizedResourceManager(context.applicationContext).also { 
                    INSTANCE = it 
                }
            }
        }
    }
    
    // 延迟初始化，避免启动时全量扫描
    private val assetProjects by lazy { scanAssetsProjects() }
    private val downloadedProjects by lazy { scanDownloadedProjects() }
    
    // 三级缓存系统
    private val l1Cache = LruCache<String, ProjectData>(CACHE_SIZE) // 内存缓存
    private val l2Cache = ConcurrentHashMap<String, WeakReference<ProjectData>>() // 弱引用缓存
    private val bitmapCache = AndroidXLruCache<String, Bitmap>(20 * 1024 * 1024) // 20MB bitmap缓存
    
    // 网络下载管理器
    private val downloadManager by lazy { ResourceDownloadManager(context) }
    
    // 协程作用域
    private val managerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 项目数据封装
     */
    data class ProjectData(
        val coloringData: ColoringData,
        val outlineBitmap: Bitmap?,
        val source: ResourceSource,
        val projectInfo: ProjectInfo
    )
    
    /**
     * 资源加载结果
     */
    sealed class ResourceLoadResult {
        data class Success(val projectData: ProjectData) : ResourceLoadResult()
        data class RequiresDownload(val projectId: String, val reason: String) : ResourceLoadResult()
        data class Error(val message: String) : ResourceLoadResult()
    }
    
    /**
     * 按需加载项目 - 主要入口方法
     */
    suspend fun loadProject(projectId: String): ResourceLoadResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔍 开始加载项目: $projectId")
            
            // 1. 检查L1缓存 (最快)
            l1Cache.get(projectId)?.let { 
                Log.d(TAG, "✅ L1缓存命中: $projectId")
                return@withContext ResourceLoadResult.Success(it)
            }
            
            // 2. 检查L2弱引用缓存
            l2Cache[projectId]?.get()?.let { data ->
                Log.d(TAG, "✅ L2缓存命中: $projectId")
                l1Cache.put(projectId, data)
                return@withContext ResourceLoadResult.Success(data)
            }
            
            // 3. 从本地Assets加载 (较快)
            loadFromAssets(projectId)?.let { data ->
                Log.d(TAG, "✅ 从Assets加载: $projectId")
                cacheProjectData(projectId, data)
                return@withContext ResourceLoadResult.Success(data)
            }
            
            // 4. 从已下载文件加载 (中等速度)
            loadFromDownloaded(projectId)?.let { data ->
                Log.d(TAG, "✅ 从下载缓存加载: $projectId")
                cacheProjectData(projectId, data)
                return@withContext ResourceLoadResult.Success(data)
            }
            
            // 5. 检查是否需要网络下载
            if (NetworkConfig.isNetworkFeatureEnabled()) {
                val projectInfo = getProjectInfo(projectId)
                if (projectInfo != null && !projectInfo.isDownloaded) {
                    Log.d(TAG, "🌐 项目需要下载: $projectId")
                    return@withContext ResourceLoadResult.RequiresDownload(
                        projectId, 
                        "项目需要从服务器下载"
                    )
                }
            }
            
            Log.w(TAG, "❌ 项目未找到: $projectId")
            ResourceLoadResult.Error("项目未找到: $projectId")
            
        } catch (e: Exception) {
            Log.e(TAG, "加载项目失败: $projectId", e)
            ResourceLoadResult.Error("加载失败: ${e.message}")
        }
    }
    
    /**
     * 获取所有可用项目 (延迟加载)
     */
    suspend fun getAllAvailableProjects(
        includeRemote: Boolean = true,
        forceRefresh: Boolean = false
    ): Result<List<HybridProject>> = withContext(Dispatchers.IO) {
        try {
            val projects = mutableListOf<HybridProject>()
            
            // 本地Assets项目
            val assetProjectList = if (forceRefresh) {
                scanAssetsProjects()
            } else {
                assetProjects
            }
            
            projects.addAll(assetProjectList.map { projectInfo ->
                HybridProject(
                    id = projectInfo.id,
                    displayName = projectInfo.displayName,
                    category = projectInfo.category,
                    difficulty = projectInfo.difficulty,
                    isDownloaded = true,
                    resourceSource = ResourceSource.ASSETS,
                    thumbnailPath = projectInfo.thumbnailPath,
                    version = projectInfo.version
                )
            })
            
            // 已下载项目
            val downloadedProjectList = if (forceRefresh) {
                scanDownloadedProjects()
            } else {
                downloadedProjects
            }
            
            projects.addAll(downloadedProjectList.map { projectInfo ->
                HybridProject(
                    id = projectInfo.id,
                    displayName = projectInfo.displayName,
                    category = projectInfo.category,
                    difficulty = projectInfo.difficulty,
                    isDownloaded = true,
                    resourceSource = ResourceSource.DOWNLOADED,
                    thumbnailPath = projectInfo.thumbnailPath,
                    version = projectInfo.version
                )
            })
            
            // 远程项目 (如果启用网络)
            if (includeRemote && NetworkConfig.isNetworkFeatureEnabled()) {
                try {
                    val remoteProjects = fetchRemoteProjects()
                    projects.addAll(remoteProjects)
                } catch (e: Exception) {
                    Log.w(TAG, "获取远程项目失败", e)
                }
            }
            
            Log.d(TAG, "📊 总共找到 ${projects.size} 个项目")
            Result.success(projects.distinctBy { it.id })
            
        } catch (e: Exception) {
            Log.e(TAG, "获取项目列表失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 下载项目
     */
    suspend fun downloadProject(
        projectId: String,
        onProgress: (Float) -> Unit = {}
    ): Result<ProjectData> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🌐 开始下载项目: $projectId")
            
            val downloadResult = downloadManager.downloadProject(projectId, onProgress)
            
            if (downloadResult.isSuccess) {
                val downloadedProject = downloadResult.getOrNull()!!
                
                // 加载下载的项目数据
                val projectData = loadDownloadedProjectData(downloadedProject)
                if (projectData != null) {
                    // 缓存项目数据
                    cacheProjectData(projectId, projectData)
                    
                    Log.d(TAG, "✅ 项目下载并加载成功: $projectId")
                    Result.success(projectData)
                } else {
                    Result.failure(Exception("下载的项目数据加载失败"))
                }
            } else {
                Result.failure(downloadResult.exceptionOrNull() ?: Exception("下载失败"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "下载项目失败: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 缓存项目数据
     */
    private fun cacheProjectData(projectId: String, data: ProjectData) {
        l1Cache.put(projectId, data)
        l2Cache[projectId] = WeakReference(data)
        
        // 缓存bitmap
        data.outlineBitmap?.let { bitmap ->
            bitmapCache.put("${projectId}_outline", bitmap)
        }
    }
    
    /**
     * 从Assets加载项目
     */
    private suspend fun loadFromAssets(projectId: String): ProjectData? = withContext(Dispatchers.IO) {
        try {
            val assetLoader = AssetResourceLoader(context)
            val coloringData = assetLoader.loadColoringData(projectId) ?: return@withContext null
            val outlineBitmap = assetLoader.loadOutlineBitmap(projectId)
            
            val projectInfo = assetProjects.find { it.id == projectId } 
                ?: ProjectInfo(projectId, projectId, "unknown", "easy")
            
            ProjectData(
                coloringData = coloringData,
                outlineBitmap = outlineBitmap,
                source = ResourceSource.ASSETS,
                projectInfo = projectInfo
            )
        } catch (e: Exception) {
            Log.e(TAG, "从Assets加载失败: $projectId", e)
            null
        }
    }
    
    /**
     * 从已下载文件加载项目
     */
    private suspend fun loadFromDownloaded(projectId: String): ProjectData? = withContext(Dispatchers.IO) {
        try {
            val downloadDir = File(context.filesDir, "downloaded_projects/$projectId")
            if (!downloadDir.exists()) return@withContext null
            
            val jsonFile = File(downloadDir, "$projectId.json")
            val pngFile = File(downloadDir, "$projectId.png")
            
            if (!jsonFile.exists() || !pngFile.exists()) return@withContext null
            
            val fileLoader = FileResourceLoader(context)
            val coloringData = fileLoader.loadColoringDataFromFile(jsonFile) ?: return@withContext null
            val outlineBitmap = fileLoader.loadBitmapFromFile(pngFile)
            
            val projectInfo = downloadedProjects.find { it.id == projectId }
                ?: ProjectInfo(projectId, projectId, "unknown", "easy")
            
            ProjectData(
                coloringData = coloringData,
                outlineBitmap = outlineBitmap,
                source = ResourceSource.DOWNLOADED,
                projectInfo = projectInfo
            )
        } catch (e: Exception) {
            Log.e(TAG, "从下载文件加载失败: $projectId", e)
            null
        }
    }
    
    /**
     * 扫描Assets项目 (延迟执行)
     */
    private fun scanAssetsProjects(): List<ProjectInfo> {
        return try {
            Log.d(TAG, "🔍 扫描Assets项目...")
            val enhancedAssetManager = EnhancedAssetManager(context)
            val projects = enhancedAssetManager.getAllValidatedProjects()
            Log.d(TAG, "✅ Assets扫描完成，找到 ${projects.size} 个项目")
            projects
        } catch (e: Exception) {
            Log.e(TAG, "扫描Assets失败", e)
            emptyList()
        }
    }
    
    /**
     * 扫描已下载项目 (延迟执行)
     */
    private fun scanDownloadedProjects(): List<ProjectInfo> {
        return try {
            Log.d(TAG, "🔍 扫描已下载项目...")
            val downloadDir = File(context.filesDir, "downloaded_projects")
            if (!downloadDir.exists()) return emptyList()
            
            val projects = mutableListOf<ProjectInfo>()
            downloadDir.listFiles()?.forEach { projectDir ->
                if (projectDir.isDirectory) {
                    val projectId = projectDir.name
                    val jsonFile = File(projectDir, "$projectId.json")
                    val pngFile = File(projectDir, "$projectId.png")
                    
                    if (jsonFile.exists() && pngFile.exists()) {
                        projects.add(ProjectInfo(
                            id = projectId,
                            displayName = projectId,
                            category = "downloaded",
                            difficulty = "unknown"
                        ))
                    }
                }
            }
            
            Log.d(TAG, "✅ 下载项目扫描完成，找到 ${projects.size} 个项目")
            projects
        } catch (e: Exception) {
            Log.e(TAG, "扫描下载项目失败", e)
            emptyList()
        }
    }
    
    /**
     * 获取项目信息
     */
    private fun getProjectInfo(projectId: String): HybridProject? {
        // 先检查本地项目
        assetProjects.find { it.id == projectId }?.let { projectInfo ->
            return HybridProject(
                id = projectInfo.id,
                displayName = projectInfo.displayName,
                category = projectInfo.category,
                difficulty = projectInfo.difficulty,
                isDownloaded = true,
                resourceSource = ResourceSource.ASSETS,
                thumbnailPath = projectInfo.thumbnailPath,
                version = projectInfo.version
            )
        }
        
        downloadedProjects.find { it.id == projectId }?.let { projectInfo ->
            return HybridProject(
                id = projectInfo.id,
                displayName = projectInfo.displayName,
                category = projectInfo.category,
                difficulty = projectInfo.difficulty,
                isDownloaded = true,
                resourceSource = ResourceSource.DOWNLOADED,
                thumbnailPath = projectInfo.thumbnailPath,
                version = projectInfo.version
            )
        }
        
        return null
    }
    
    /**
     * 获取远程项目列表
     */
    private suspend fun fetchRemoteProjects(): List<HybridProject> {
        return try {
            val result = downloadManager.getProjectsList(page = 1, pageSize = 50)
            if (result.isSuccess) {
                val response = result.getOrNull()
                response?.data?.projects?.map { serverProject ->
                    HybridProject(
                        id = serverProject.id,
                        displayName = serverProject.displayName,
                        category = serverProject.category,
                        difficulty = serverProject.difficulty,
                        isDownloaded = false,
                        resourceSource = ResourceSource.REMOTE,
                        thumbnailPath = serverProject.thumbnailUrl,
                        version = serverProject.version
                    )
                } ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取远程项目失败", e)
            emptyList()
        }
    }
    
    /**
     * 清理缓存
     */
    fun clearCache() {
        l1Cache.evictAll()
        l2Cache.clear()
        bitmapCache.evictAll()
        Log.d(TAG, "🧹 缓存已清理")
    }
    
    /**
     * 内存压力处理
     */
    fun onMemoryPressure(level: Int) {
        when (level) {
            android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE -> {
                l1Cache.evictAll()
                Log.d(TAG, "🧹 中等内存压力，清理L1缓存")
            }
            android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW -> {
                l1Cache.evictAll()
                l2Cache.clear()
                bitmapCache.evictAll()
                Log.d(TAG, "🧹 低内存压力，清理所有缓存")
            }
        }
    }
    
    /**
     * 加载下载的项目数据
     */
    private suspend fun loadDownloadedProjectData(downloadedProject: ResourceDownloadManager.DownloadedProject): ProjectData? {
        return try {
            val fileLoader = FileResourceLoader(context)
            val coloringData = fileLoader.loadColoringDataFromFile(downloadedProject.jsonFile)
                ?: return null
            val outlineBitmap = fileLoader.loadBitmapFromFile(downloadedProject.outlineFile)
            
            ProjectData(
                coloringData = coloringData,
                outlineBitmap = outlineBitmap,
                source = ResourceSource.DOWNLOADED,
                projectInfo = ProjectInfo(
                    id = downloadedProject.projectId,
                    displayName = downloadedProject.projectId,
                    category = "downloaded",
                    difficulty = "unknown"
                )
            )
        } catch (e: Exception) {
            Log.e(TAG, "加载下载项目数据失败", e)
            null
        }
    }
}
