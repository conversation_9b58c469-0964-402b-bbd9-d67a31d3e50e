package com.example.coloringproject.utils

import android.content.ComponentCallbacks2
import android.content.Context
import android.util.Log
import android.util.LruCache
import kotlinx.coroutines.*
import java.io.File
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 统一缓存管理器
 * 实现三级缓存架构：内存缓存 -> 弱引用缓存 -> 磁盘缓存
 */
class UnifiedCacheManager(private val context: Context) {
    
    companion object {
        private const val TAG = "UnifiedCacheManager"
        private const val L1_CACHE_SIZE = 20 // L1内存缓存大小
        private const val DISK_CACHE_SIZE = 50 * 1024 * 1024L // 50MB磁盘缓存
        
        @Volatile
        private var INSTANCE: UnifiedCacheManager? = null
        
        fun getInstance(context: Context): UnifiedCacheManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UnifiedCacheManager(context.applicationContext).also { 
                    INSTANCE = it 
                }
            }
        }
    }
    
    // 三级缓存架构
    private val l1Cache = LruCache<String, CacheData>(L1_CACHE_SIZE)           // L1: 内存缓存
    private val l2Cache = ConcurrentHashMap<String, WeakReference<CacheData>>() // L2: 弱引用缓存
    private val l3CacheDir = File(context.cacheDir, "unified_cache")           // L3: 磁盘缓存
    
    // 缓存作用域
    private val cacheScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 缓存统计
    private var l1Hits = 0
    private var l2Hits = 0
    private var l3Hits = 0
    private var misses = 0
    
    init {
        // 确保磁盘缓存目录存在
        if (!l3CacheDir.exists()) {
            l3CacheDir.mkdirs()
        }
        
        Log.d(TAG, "✅ 统一缓存管理器初始化完成")
    }
    
    /**
     * 缓存数据封装
     */
    data class CacheData(
        val key: String,
        val data: Any,
        val timestamp: Long,
        val size: Long = 0L,
        val type: CacheType
    )
    
    /**
     * 缓存类型
     */
    enum class CacheType {
        PROGRESS_DATA,      // 进度数据
        PROJECT_DATA,       // 项目数据
        BITMAP_DATA,        // 位图数据
        JSON_DATA,          // JSON数据
        THUMBNAIL_DATA      // 缩略图数据
    }
    
    /**
     * 缓存结果
     */
    sealed class CacheResult<T> {
        data class Hit<T>(val data: T, val source: CacheSource) : CacheResult<T>()
        class Miss<T> : CacheResult<T>()
    }
    
    /**
     * 缓存来源
     */
    enum class CacheSource {
        L1_MEMORY,      // L1内存缓存
        L2_WEAK_REF,    // L2弱引用缓存
        L3_DISK         // L3磁盘缓存
    }
    
    /**
     * 获取缓存数据 - 主要入口方法
     */
    suspend fun <T> get(key: String, type: CacheType): CacheResult<T> = withContext(Dispatchers.IO) {
        try {
            // L1: 内存缓存
            l1Cache.get(key)?.let { cacheData ->
                if (cacheData.type == type) {
                    l1Hits++
                    Log.d(TAG, "✅ L1缓存命中: $key")
                    @Suppress("UNCHECKED_CAST")
                    return@withContext CacheResult.Hit(cacheData.data as T, CacheSource.L1_MEMORY)
                }
            }
            
            // L2: 弱引用缓存
            l2Cache[key]?.get()?.let { cacheData ->
                if (cacheData.type == type) {
                    l2Hits++
                    Log.d(TAG, "✅ L2缓存命中: $key")
                    
                    // 提升到L1缓存
                    l1Cache.put(key, cacheData)
                    
                    @Suppress("UNCHECKED_CAST")
                    return@withContext CacheResult.Hit(cacheData.data as T, CacheSource.L2_WEAK_REF)
                }
            }
            
            // L3: 磁盘缓存
            loadFromDiskCache<T>(key, type)?.let { data ->
                l3Hits++
                Log.d(TAG, "✅ L3缓存命中: $key")
                
                // 提升到L1和L2缓存
                val cacheData = CacheData(key, data, System.currentTimeMillis(), 0L, type)
                l1Cache.put(key, cacheData)
                l2Cache[key] = WeakReference(cacheData)
                
                return@withContext CacheResult.Hit(data, CacheSource.L3_DISK)
            }
            
            // 缓存未命中
            misses++
            Log.d(TAG, "❌ 缓存未命中: $key")
            CacheResult.Miss<T>()
            
        } catch (e: Exception) {
            Log.e(TAG, "获取缓存失败: $key", e)
            CacheResult.Miss<T>()
        }
    }
    
    /**
     * 存储缓存数据
     */
    suspend fun put(key: String, data: Any, type: CacheType, persistToDisk: Boolean = true) = withContext(Dispatchers.IO) {
        try {
            val cacheData = CacheData(
                key = key,
                data = data,
                timestamp = System.currentTimeMillis(),
                size = estimateDataSize(data),
                type = type
            )
            
            // 存储到L1缓存
            l1Cache.put(key, cacheData)
            
            // 存储到L2缓存
            l2Cache[key] = WeakReference(cacheData)
            
            // 异步存储到磁盘缓存
            if (persistToDisk) {
                cacheScope.launch {
                    saveToDiskCache(key, data, type)
                }
            }
            
            Log.d(TAG, "💾 缓存已存储: $key (类型: $type)")
            
        } catch (e: Exception) {
            Log.e(TAG, "存储缓存失败: $key", e)
        }
    }
    
    /**
     * 从磁盘缓存加载
     */
    private suspend fun <T> loadFromDiskCache(key: String, type: CacheType): T? = withContext(Dispatchers.IO) {
        try {
            val cacheFile = File(l3CacheDir, "${key}_${type.name}.cache")
            if (!cacheFile.exists()) return@withContext null
            
            // 检查缓存是否过期 (7天)
            val maxAge = 7 * 24 * 60 * 60 * 1000L
            if (System.currentTimeMillis() - cacheFile.lastModified() > maxAge) {
                cacheFile.delete()
                return@withContext null
            }
            
            val content = cacheFile.readText()
            
            // 根据类型反序列化数据
            @Suppress("UNCHECKED_CAST")
            when (type) {
                CacheType.PROGRESS_DATA -> {
                    // 进度数据反序列化
                    deserializeProgressData(content) as? T
                }
                CacheType.JSON_DATA -> {
                    // JSON数据直接返回
                    content as? T
                }
                else -> {
                    // 其他类型暂不支持磁盘缓存
                    null
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "从磁盘缓存加载失败: $key", e)
            null
        }
    }
    
    /**
     * 保存到磁盘缓存
     */
    private suspend fun saveToDiskCache(key: String, data: Any, type: CacheType) = withContext(Dispatchers.IO) {
        try {
            val cacheFile = File(l3CacheDir, "${key}_${type.name}.cache")
            
            // 根据类型序列化数据
            val content = when (type) {
                CacheType.PROGRESS_DATA -> {
                    serializeProgressData(data)
                }
                CacheType.JSON_DATA -> {
                    data.toString()
                }
                else -> {
                    // 其他类型暂不支持磁盘缓存
                    return@withContext
                }
            }
            
            cacheFile.writeText(content)
            Log.d(TAG, "💾 磁盘缓存已保存: $key")
            
        } catch (e: Exception) {
            Log.e(TAG, "保存到磁盘缓存失败: $key", e)
        }
    }
    
    /**
     * 清理缓存
     */
    fun clearCache(level: CacheLevel = CacheLevel.ALL) {
        when (level) {
            CacheLevel.L1_ONLY -> {
                l1Cache.evictAll()
                Log.d(TAG, "🧹 L1缓存已清理")
            }
            CacheLevel.L1_L2 -> {
                l1Cache.evictAll()
                l2Cache.clear()
                Log.d(TAG, "🧹 L1和L2缓存已清理")
            }
            CacheLevel.ALL -> {
                l1Cache.evictAll()
                l2Cache.clear()
                clearDiskCache()
                Log.d(TAG, "🧹 所有缓存已清理")
            }
        }
    }
    
    /**
     * 缓存清理级别
     */
    enum class CacheLevel {
        L1_ONLY,    // 仅L1缓存
        L1_L2,      // L1和L2缓存
        ALL         // 所有缓存
    }
    
    /**
     * 清理磁盘缓存
     */
    private fun clearDiskCache() {
        cacheScope.launch {
            try {
                l3CacheDir.listFiles()?.forEach { file ->
                    file.delete()
                }
                Log.d(TAG, "🧹 磁盘缓存已清理")
            } catch (e: Exception) {
                Log.e(TAG, "清理磁盘缓存失败", e)
            }
        }
    }
    
    /**
     * 内存压力处理
     */
    fun onMemoryPressure(level: Int) {
        when (level) {
            ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE -> {
                clearCache(CacheLevel.L1_ONLY)
            }
            ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW -> {
                clearCache(CacheLevel.L1_L2)
            }
            ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL -> {
                clearCache(CacheLevel.ALL)
            }
        }
    }
    
    /**
     * 获取缓存统计
     */
    fun getCacheStats(): String {
        val total = l1Hits + l2Hits + l3Hits + misses
        val hitRate = if (total > 0) ((l1Hits + l2Hits + l3Hits) * 100.0 / total) else 0.0
        
        return buildString {
            appendLine("=== 缓存统计 ===")
            appendLine("L1命中: $l1Hits")
            appendLine("L2命中: $l2Hits") 
            appendLine("L3命中: $l3Hits")
            appendLine("未命中: $misses")
            appendLine("命中率: ${"%.1f".format(hitRate)}%")
            appendLine("L1大小: ${l1Cache.size()}/${L1_CACHE_SIZE}")
            appendLine("L2大小: ${l2Cache.size}")
        }
    }
    
    /**
     * 估算数据大小
     */
    private fun estimateDataSize(data: Any): Long {
        return when (data) {
            is String -> data.length.toLong() * 2 // UTF-16
            is ByteArray -> data.size.toLong()
            else -> 1024L // 默认1KB
        }
    }
    
    /**
     * 序列化进度数据
     */
    private fun serializeProgressData(data: Any): String {
        // 简单的JSON序列化，实际项目中可以使用Gson
        return data.toString()
    }
    
    /**
     * 反序列化进度数据
     */
    private fun deserializeProgressData(content: String): Any? {
        // 简单的反序列化，实际项目中可以使用Gson
        return content
    }
    
    /**
     * 销毁缓存管理器
     */
    fun destroy() {
        clearCache(CacheLevel.ALL)
        cacheScope.cancel()
        Log.d(TAG, "🔚 UnifiedCacheManager已销毁")
    }
}
