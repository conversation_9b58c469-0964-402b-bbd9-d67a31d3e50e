<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@drawable/splash_gradient_background"
    android:padding="32dp">

    <!-- 应用Logo -->
    <ImageView
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:src="@mipmap/ic_launcher"
        android:layout_marginBottom="32dp"
        android:scaleType="centerCrop"
        android:elevation="8dp" />

    <!-- 应用名称 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="涂色项目"
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="#FFFFFF"
        android:layout_marginBottom="8dp"
        android:shadowColor="#80000000"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="4" />

    <!-- 副标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="优化版本 - 快速启动"
        android:textSize="16sp"
        android:textColor="#E0FFFFFF"
        android:layout_marginBottom="64dp" />

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/splashProgressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="250dp"
        android:layout_height="6dp"
        android:max="100"
        android:progress="0"
        android:progressTint="#FFFFFF"
        android:progressBackgroundTint="#40FFFFFF"
        android:layout_marginBottom="16dp" />

    <!-- 状态文本 -->
    <TextView
        android:id="@+id/splashStatusText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="正在启动..."
        android:textSize="14sp"
        android:textColor="#E0FFFFFF"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <!-- 版本信息 -->
    <TextView
        android:id="@+id/splashVersionText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="版本 1.0"
        android:textSize="12sp"
        android:textColor="#80FFFFFF"
        android:layout_marginTop="32dp" />

    <!-- 优化特性列表 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="24dp"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="• 统一资源管理"
            android:textSize="11sp"
            android:textColor="#C0FFFFFF"
            android:layout_marginBottom="2dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="• 异步初始化"
            android:textSize="11sp"
            android:textColor="#C0FFFFFF"
            android:layout_marginBottom="2dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="• 智能缓存"
            android:textSize="11sp"
            android:textColor="#C0FFFFFF" />

    </LinearLayout>

</LinearLayout>
