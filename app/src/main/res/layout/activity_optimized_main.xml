<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF">

    <!-- 涂色视图 -->
    <com.example.coloringproject.views.ColoringView
        android:id="@+id/coloringView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <!-- 启动画面容器 -->
    <LinearLayout
        android:id="@+id/splashContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:background="#F5F5F5"
        android:padding="32dp">

        <!-- 应用图标 -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@mipmap/ic_launcher"
            android:layout_marginBottom="32dp"
            android:scaleType="centerCrop" />

        <!-- 应用标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="涂色项目"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="16dp" />

        <!-- 版本信息 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="优化版本 v2.0"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="48dp" />

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/loadingProgressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="200dp"
            android:layout_height="8dp"
            android:max="100"
            android:progress="0"
            android:progressTint="#4CAF50"
            android:progressBackgroundTint="#E0E0E0"
            android:layout_marginBottom="16dp" />

        <!-- 加载状态文本 -->
        <TextView
            android:id="@+id/loadingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="正在启动..."
            android:textSize="16sp"
            android:textColor="#666666"
            android:gravity="center" />

        <!-- 功能状态指示 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="32dp"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="✓ 统一资源管理"
                android:textSize="12sp"
                android:textColor="#4CAF50"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="✓ 异步初始化"
                android:textSize="12sp"
                android:textColor="#4CAF50"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="✓ 智能缓存"
                android:textSize="12sp"
                android:textColor="#4CAF50"
                android:layout_marginBottom="4dp" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
